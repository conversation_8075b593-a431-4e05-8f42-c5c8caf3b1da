<!--
  ~ Copyright (C) 2024 OpenAni and contributors.
  ~
  ~ 此源代码的使用受 GNU AFFERO GENERAL PUBLIC LICENSE version 3 许可证的约束, 可以在以下链接找到该许可证.
  ~ Use of this source code is governed by the GNU AGPLv3 license, which can be found at the following link.
  ~
  ~ https://github.com/open-ani/ani/blob/main/LICENSE
  -->

<component name="ProjectRunConfigurationManager">
    <configuration default="false" name="GenerateTestsKt" type="JetRunConfigurationType"
            nameIsGenerated="true">
        <option name="MAIN_CLASS_NAME"
                value="me.him188.ani.datasources.api.test.codegen.main.GenerateTestsKt" />
        <module name="animeko.datasource.datasource-api.test-codegen.test" />
        <shortenClasspath name="NONE" />
        <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/datasource/api/test-codegen" />
        <method v="2">
            <option name="Make" enabled="true" />
        </method>
    </configuration>
</component>