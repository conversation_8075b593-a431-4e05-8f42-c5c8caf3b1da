# This file was generated using Kotlin DSL (.github/workflows/src.main.kts).
# If you want to modify the workflow, please change the Kotlin file and regenerate this YAML file.
# Generated with https://github.com/typesafegithub/github-workflows-kt

name: 'Release'
on:
  push:
    tags:
    - 'v*'
permissions:
  actions: 'write'
  contents: 'write'
jobs:
  consistency-check:
    name: 'Workflow YAML Consistency Check'
    runs-on: 'ubuntu-latest'
    permissions: {}
    steps:
    - id: 'step-0'
      uses: 'actions/checkout@v4'
    - id: 'step-1'
      run: 'pip3 install PyYAML'
    - id: 'step-2'
      run: 'cp ".github/workflows/release.yml" ".github/workflows/release.yml-check.yml" '
    - id: 'step-3'
      run: '.github/workflows/src.main.kts'
    - id: 'step-4'
      run: 'python .github/workflows/check_yaml_equivalence.py .github/workflows/release.yml .github/workflows/release.yml-check.yml'
  create-release:
    name: 'Create Release'
    runs-on: 'ubuntu-latest'
    outputs:
      uploadUrl: '${{ steps.step-4.outputs.upload_url }}'
      id: '${{ steps.step-4.outputs.id }}'
    steps:
    - id: 'step-0'
      uses: 'actions/checkout@v4'
    - id: 'step-1'
      name: 'Get Tag'
      uses: 'dawidd6/action-get-tag@v1'
    - id: 'step-2'
      uses: 'bhowell2/github-substring-action@v1.0.0'
      with:
        value: '${{ steps.step-1.outputs.tag }}'
        index_of_str: 'v'
        default_return_value: '${{ steps.step-1.outputs.tag }}'
    - id: 'step-3'
      name: 'Generate Release Notes'
      run: |-
        # Specify the file path
        FILE_PATH="ci-helper/release-template.md"

        # Read the file content
        file_content=$(cat "$FILE_PATH")

        modified_content="$file_content"
        # Replace 'string_to_find' with 'string_to_replace_with' in the content
        modified_content="${modified_content//\$GIT_TAG/${{ steps.step-1.outputs.tag }}}"
        modified_content="${modified_content//\$TAG_VERSION/${{ steps.step-2.outputs.substring }}}"

        # Output the result as a step output
        echo "result<<EOF" >> $GITHUB_OUTPUT
        echo "$modified_content" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
    - id: 'step-4'
      name: 'Create Release'
      uses: 'softprops/action-gh-release@v1'
      with:
        body: '${{ steps.step-3.outputs.result }}'
        name: '${{ steps.step-2.outputs.substring }}'
        tag_name: '${{ steps.step-1.outputs.tag }}'
        draft: 'true'
        prerelease: '${{ contains(steps.step-1.outputs.tag, ''-'') }}'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
  release_github-windows-2022:
    name: 'Windows Server 2019 x86_64'
    runs-on:
    - 'windows-2022'
    needs:
    - 'create-release'
    steps:
    - id: 'step-0'
      uses: 'actions/checkout@v4'
      with:
        submodules: 'recursive'
    - id: 'step-1'
      name: 'Get Tag'
      uses: 'dawidd6/action-get-tag@v1'
    - id: 'step-2'
      uses: 'bhowell2/github-substring-action@v1.0.0'
      with:
        value: '${{ steps.step-1.outputs.tag }}'
        index_of_str: 'v'
        default_return_value: '${{ steps.step-1.outputs.tag }}'
    - id: 'step-3'
      continue-on-error: true
      run: 'rm local.properties'
    - id: 'step-4'
      continue-on-error: true
      run: |-
        echo "ani.dandanplay.app.id=${{ secrets.DANDANPLAY_APP_ID }}" >> local.properties
        echo "ani.dandanplay.app.secret=${{ secrets.DANDANPLAY_APP_SECRET }}" >> local.properties
        echo "ani.sentry.dsn=${{ secrets.SENTRY_DSN }}" >> local.properties
        echo "ani.analytics.server=${{ secrets.ANALYTICS_SERVER }}" >> local.properties
        echo "ani.analytics.key=${{ secrets.ANALYTICS_KEY }}" >> local.properties
        echo "kotlin.native.ignoreDisabledTargets=true" >> local.properties
    - id: 'step-5'
      name: 'Get JBR (Windows)'
      env:
        RUNNER_TOOL_CACHE: '${{ runner.tool_cache }}'
        JBR_URL: 'https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.5-windows-x64-b750.29.tar.gz'
        JBR_CHECKSUM_URL: 'https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.5-windows-x64-b750.29.tar.gz.checksum'
      shell: 'cmd'
      run: 'python .github/workflows/download_jbr.py'
    - id: 'step-6'
      name: 'Setup JBR 21 for Windows'
      uses: 'gmitch215/setup-java@6d2c5e1f82f180ae79f799f0ed6e3e5efb4e664d'
      with:
        java-version: '21'
        distribution: 'jdkfile'
        jdkFile: '${{ steps.step-5.outputs.jbrLocation }}'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
    - id: 'step-7'
      name: 'Dump Local Properties'
      run: 'echo "jvm.toolchain.version=21" >> local.properties'
    - id: 'step-8'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v3'
      with:
        cache-disabled: 'true'
    - id: 'step-9'
      name: 'Clean and download dependencies (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
    - id: 'step-10'
      name: 'Clean and download dependencies (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
      if: '${{ steps.step-9.outcome == ''failure'' }}'
    - id: 'step-11'
      name: 'Update Release Version Name (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
    - id: 'step-12'
      name: 'Update Release Version Name (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
      if: '${{ steps.step-11.outcome == ''failure'' }}'
    - id: 'step-13'
      name: 'Compile Kotlin (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
    - id: 'step-14'
      name: 'Compile Kotlin (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
      if: '${{ steps.step-13.outcome == ''failure'' }}'
    - id: 'step-15'
      name: 'Compile Kotlin Android (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileDebugKotlinAndroid compileReleaseKotlinAndroid "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
    - id: 'step-16'
      name: 'Compile Kotlin Android (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileDebugKotlinAndroid compileReleaseKotlinAndroid "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
      if: '${{ steps.step-15.outcome == ''failure'' }}'
    - id: 'step-17'
      name: 'Upload Desktop Installers (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
    - id: 'step-18'
      name: 'Upload Desktop Installers (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DBoost_INCLUDE_DIR=C:/vcpkg/installed/x64-windows/include" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "--parallel" "-Pani.android.abis=x86_64"'
      if: '${{ steps.step-17.outcome == ''failure'' }}'
    - id: 'step-19'
      name: 'Upload compose logs'
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'compose-logs-github-windows-2022'
        path: 'app/desktop/build/compose/logs'
      if: '${{ always() }}'
  release_self-hosted-macos-15:
    name: 'macOS 15 AArch64 (Self-Hosted)'
    runs-on:
    - 'self-hosted'
    - 'macOS'
    - 'ARM64'
    needs:
    - 'create-release'
    if: '${{ github.repository == ''open-ani/animeko'' }}'
    steps:
    - id: 'step-0'
      uses: 'actions/checkout@v4'
      with:
        submodules: 'recursive'
    - id: 'step-1'
      name: 'Get Tag'
      uses: 'dawidd6/action-get-tag@v1'
    - id: 'step-2'
      uses: 'bhowell2/github-substring-action@v1.0.0'
      with:
        value: '${{ steps.step-1.outputs.tag }}'
        index_of_str: 'v'
        default_return_value: '${{ steps.step-1.outputs.tag }}'
    - id: 'step-3'
      continue-on-error: true
      run: 'rm local.properties'
    - id: 'step-4'
      continue-on-error: true
      run: |-
        echo "ani.dandanplay.app.id=${{ secrets.DANDANPLAY_APP_ID }}" >> local.properties
        echo "ani.dandanplay.app.secret=${{ secrets.DANDANPLAY_APP_SECRET }}" >> local.properties
        echo "ani.sentry.dsn=${{ secrets.SENTRY_DSN }}" >> local.properties
        echo "ani.analytics.server=${{ secrets.ANALYTICS_SERVER }}" >> local.properties
        echo "ani.analytics.key=${{ secrets.ANALYTICS_KEY }}" >> local.properties
        echo "kotlin.native.ignoreDisabledTargets=true" >> local.properties
    - id: 'step-5'
      name: 'Update JVM args in gradle.properties'
      run: |-
        # Update org.gradle.jvmargs in gradle.properties
        if grep -q '^org.gradle.jvmargs=' gradle.properties; then
          # replace the existing line
          sed -i -E 's/^org.gradle.jvmargs=.*/org.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g/' gradle.properties
        else
          # append if the key is not present
          echo "org.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" >> gradle.properties
        fi
    - id: 'step-6'
      name: 'Display gradle.properties'
      run: 'cat ./gradle.properties'
    - id: 'step-7'
      name: 'Resolve JBR location'
      shell: 'bash'
      run: |-
        # Expand jbrLocationExpr
        jbr_location_expr='${{ runner.tool_cache }}/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz'
        echo "jbrLocation=$jbr_location_expr" >> $GITHUB_OUTPUT
    - id: 'step-8'
      name: 'Get JBR 21 for macOS AArch64 (Attempt #1)'
      env:
        jbrLocation: '${{ steps.step-7.outputs.jbrLocation }}'
      continue-on-error: true
      timeout-minutes: 180
      shell: 'bash'
      run: |-
        jbr_location="$jbrLocation"
        checksum_url="https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz.checksum"
        checksum_file="checksum.tmp"
        wget -q -O $checksum_file $checksum_url

        expected_checksum=$(awk '{print $1}' $checksum_file)
        file_checksum=""

        if [ -f "$jbr_location" ]; then
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            wget -q --tries=3 https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz -O "$jbr_location"
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            echo "Checksum verification failed!" >&2
            rm -f $checksum_file
            exit 1
        fi

        rm -f $checksum_file
        file "$jbr_location"
    - id: 'step-9'
      name: 'Get JBR 21 for macOS AArch64 (Attempt #2)'
      env:
        jbrLocation: '${{ steps.step-7.outputs.jbrLocation }}'
      continue-on-error: false
      timeout-minutes: 180
      shell: 'bash'
      run: |-
        jbr_location="$jbrLocation"
        checksum_url="https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz.checksum"
        checksum_file="checksum.tmp"
        wget -q -O $checksum_file $checksum_url

        expected_checksum=$(awk '{print $1}' $checksum_file)
        file_checksum=""

        if [ -f "$jbr_location" ]; then
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            wget -q --tries=3 https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz -O "$jbr_location"
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            echo "Checksum verification failed!" >&2
            rm -f $checksum_file
            exit 1
        fi

        rm -f $checksum_file
        file "$jbr_location"
      if: '${{ steps.step-8.outcome == ''failure'' }}'
    - id: 'step-10'
      name: 'Setup JBR 21 for macOS '
      uses: 'gmitch215/setup-java@6d2c5e1f82f180ae79f799f0ed6e3e5efb4e664d'
      with:
        java-version: '21'
        distribution: 'jdkfile'
        jdkFile: '${{ steps.step-7.outputs.jbrLocation }}'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
    - id: 'step-11'
      name: 'Dump Local Properties'
      run: 'echo "jvm.toolchain.version=21" >> local.properties'
    - id: 'step-12'
      run: 'chmod -R 777 .'
    - id: 'step-13'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v3'
      with:
        cache-disabled: 'true'
    - id: 'step-14'
      name: 'Clean and download dependencies (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-15'
      name: 'Clean and download dependencies (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-14.outcome == ''failure'' }}'
    - id: 'step-16'
      name: 'Update Release Version Name (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-17'
      name: 'Update Release Version Name (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-16.outcome == ''failure'' }}'
    - id: 'step-18'
      name: 'Compile Kotlin (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-19'
      name: 'Compile Kotlin (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-18.outcome == ''failure'' }}'
    - id: 'step-20'
      name: 'Compile Kotlin Android (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileDebugKotlinAndroid compileReleaseKotlinAndroid "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-21'
      name: 'Compile Kotlin Android (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileDebugKotlinAndroid compileReleaseKotlinAndroid "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-20.outcome == ''failure'' }}'
    - id: 'step-22'
      name: 'generateDummyFramework (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :app:shared:application:generateDummyFramework "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-23'
      name: 'generateDummyFramework (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :app:shared:application:generateDummyFramework "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-22.outcome == ''failure'' }}'
    - id: 'step-24'
      name: 'Pod Install (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :app:ios:podInstall "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-25'
      name: 'Pod Install (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :app:ios:podInstall "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-24.outcome == ''failure'' }}'
    - id: 'step-26'
      name: 'Patch ios Plist (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :app:ios:patchInfoPlist "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-27'
      name: 'Patch ios Plist (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :app:ios:patchInfoPlist "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-26.outcome == ''failure'' }}'
    - id: 'step-28'
      name: 'Build iOS Release IPA (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :app:ios:buildReleaseIpa "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-29'
      name: 'Build iOS Release IPA (Attempt #2)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :app:ios:buildReleaseIpa "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-28.outcome == ''failure'' }}'
    - id: 'step-30'
      name: 'Build iOS Release IPA (Attempt #3)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :app:ios:buildReleaseIpa "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-29.outcome == ''failure'' }}'
    - id: 'step-31'
      name: 'Upload iOS Release IPA (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-ios-release'
        path: 'app/ios/build/archives/release/Animeko.ipa'
        overwrite: 'true'
    - id: 'step-32'
      name: 'Upload iOS Release IPA (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-ios-release'
        path: 'app/ios/build/archives/release/Animeko.ipa'
        overwrite: 'true'
      if: '${{ steps.step-31.outcome == ''failure'' }}'
    - id: 'step-33'
      name: 'Upload iOS Release IPA (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-ios-release'
        path: 'app/ios/build/archives/release/Animeko.ipa'
        overwrite: 'true'
      if: '${{ steps.step-32.outcome == ''failure'' }}'
    - id: 'step-34'
      name: 'Upload iOS IPA (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadIosIpa "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
    - id: 'step-35'
      name: 'Upload iOS IPA (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadIosIpa "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx16g" "-Dkotlin.daemon.jvm.options=-Xmx16g" "--parallel"'
      if: '${{ steps.step-34.outcome == ''failure'' }}'
    - id: 'step-36'
      name: 'Cleanup temp files'
      continue-on-error: true
      run: 'chmod +x ./ci-helper/cleanup-temp-files-macos.sh && ./ci-helper/cleanup-temp-files-macos.sh'
  release_github-macos-15:
    name: 'macOS 15 AArch64 (GitHub)'
    runs-on:
    - 'macos-15'
    needs:
    - 'create-release'
    steps:
    - id: 'step-0'
      uses: 'actions/checkout@v4'
      with:
        submodules: 'recursive'
    - id: 'step-1'
      name: 'Get Tag'
      uses: 'dawidd6/action-get-tag@v1'
    - id: 'step-2'
      uses: 'bhowell2/github-substring-action@v1.0.0'
      with:
        value: '${{ steps.step-1.outputs.tag }}'
        index_of_str: 'v'
        default_return_value: '${{ steps.step-1.outputs.tag }}'
    - id: 'step-3'
      name: 'Free space for macOS'
      continue-on-error: true
      run: 'chmod +x ./ci-helper/free-space-macos.sh && ./ci-helper/free-space-macos.sh'
    - id: 'step-4'
      continue-on-error: true
      run: 'rm local.properties'
    - id: 'step-5'
      continue-on-error: true
      run: |-
        echo "ani.dandanplay.app.id=${{ secrets.DANDANPLAY_APP_ID }}" >> local.properties
        echo "ani.dandanplay.app.secret=${{ secrets.DANDANPLAY_APP_SECRET }}" >> local.properties
        echo "ani.sentry.dsn=${{ secrets.SENTRY_DSN }}" >> local.properties
        echo "ani.analytics.server=${{ secrets.ANALYTICS_SERVER }}" >> local.properties
        echo "ani.analytics.key=${{ secrets.ANALYTICS_KEY }}" >> local.properties
        echo "kotlin.native.ignoreDisabledTargets=true" >> local.properties
    - id: 'step-6'
      name: 'Resolve JBR location'
      shell: 'bash'
      run: |-
        # Expand jbrLocationExpr
        jbr_location_expr='${{ runner.tool_cache }}/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz'
        echo "jbrLocation=$jbr_location_expr" >> $GITHUB_OUTPUT
    - id: 'step-7'
      name: 'Get JBR 21 for macOS AArch64 (Attempt #1)'
      env:
        jbrLocation: '${{ steps.step-6.outputs.jbrLocation }}'
      continue-on-error: true
      timeout-minutes: 180
      shell: 'bash'
      run: |-
        jbr_location="$jbrLocation"
        checksum_url="https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz.checksum"
        checksum_file="checksum.tmp"
        wget -q -O $checksum_file $checksum_url

        expected_checksum=$(awk '{print $1}' $checksum_file)
        file_checksum=""

        if [ -f "$jbr_location" ]; then
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            wget -q --tries=3 https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz -O "$jbr_location"
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            echo "Checksum verification failed!" >&2
            rm -f $checksum_file
            exit 1
        fi

        rm -f $checksum_file
        file "$jbr_location"
    - id: 'step-8'
      name: 'Get JBR 21 for macOS AArch64 (Attempt #2)'
      env:
        jbrLocation: '${{ steps.step-6.outputs.jbrLocation }}'
      continue-on-error: false
      timeout-minutes: 180
      shell: 'bash'
      run: |-
        jbr_location="$jbrLocation"
        checksum_url="https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz.checksum"
        checksum_file="checksum.tmp"
        wget -q -O $checksum_file $checksum_url

        expected_checksum=$(awk '{print $1}' $checksum_file)
        file_checksum=""

        if [ -f "$jbr_location" ]; then
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            wget -q --tries=3 https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.8-osx-aarch64-b1038.68.tar.gz -O "$jbr_location"
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            echo "Checksum verification failed!" >&2
            rm -f $checksum_file
            exit 1
        fi

        rm -f $checksum_file
        file "$jbr_location"
      if: '${{ steps.step-7.outcome == ''failure'' }}'
    - id: 'step-9'
      name: 'Setup JBR 21 for macOS '
      uses: 'gmitch215/setup-java@6d2c5e1f82f180ae79f799f0ed6e3e5efb4e664d'
      with:
        java-version: '21'
        distribution: 'jdkfile'
        jdkFile: '${{ steps.step-6.outputs.jbrLocation }}'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
    - id: 'step-10'
      name: 'Dump Local Properties'
      run: 'echo "jvm.toolchain.version=21" >> local.properties'
    - id: 'step-11'
      run: 'chmod -R 777 .'
    - id: 'step-12'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v3'
      with:
        cache-disabled: 'true'
    - id: 'step-13'
      name: 'Clean and download dependencies (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-14'
      name: 'Clean and download dependencies (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-13.outcome == ''failure'' }}'
    - id: 'step-15'
      name: 'Update Release Version Name (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-16'
      name: 'Update Release Version Name (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-15.outcome == ''failure'' }}'
    - id: 'step-17'
      name: 'Compile Kotlin (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-18'
      name: 'Compile Kotlin (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-17.outcome == ''failure'' }}'
    - id: 'step-19'
      name: 'Upload Desktop Installers (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-20'
      name: 'Upload Desktop Installers (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx4g" "-Dkotlin.daemon.jvm.options=-Xmx4g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-19.outcome == ''failure'' }}'
    - id: 'step-21'
      name: 'Upload compose logs'
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'compose-logs-github-macos-15'
        path: 'app/desktop/build/compose/logs'
      if: '${{ always() }}'
  release_github-macos-13:
    name: 'macOS 13 x86_64 (GitHub)'
    runs-on:
    - 'macos-13'
    needs:
    - 'create-release'
    steps:
    - id: 'step-0'
      uses: 'actions/checkout@v4'
      with:
        submodules: 'recursive'
    - id: 'step-1'
      name: 'Get Tag'
      uses: 'dawidd6/action-get-tag@v1'
    - id: 'step-2'
      uses: 'bhowell2/github-substring-action@v1.0.0'
      with:
        value: '${{ steps.step-1.outputs.tag }}'
        index_of_str: 'v'
        default_return_value: '${{ steps.step-1.outputs.tag }}'
    - id: 'step-3'
      name: 'Free space for macOS'
      continue-on-error: true
      run: 'chmod +x ./ci-helper/free-space-macos.sh && ./ci-helper/free-space-macos.sh'
    - id: 'step-4'
      continue-on-error: true
      run: 'rm local.properties'
    - id: 'step-5'
      continue-on-error: true
      run: |-
        echo "ani.dandanplay.app.id=${{ secrets.DANDANPLAY_APP_ID }}" >> local.properties
        echo "ani.dandanplay.app.secret=${{ secrets.DANDANPLAY_APP_SECRET }}" >> local.properties
        echo "ani.sentry.dsn=${{ secrets.SENTRY_DSN }}" >> local.properties
        echo "ani.analytics.server=${{ secrets.ANALYTICS_SERVER }}" >> local.properties
        echo "ani.analytics.key=${{ secrets.ANALYTICS_KEY }}" >> local.properties
        echo "kotlin.native.ignoreDisabledTargets=true" >> local.properties
    - id: 'step-6'
      name: 'Resolve JBR location'
      shell: 'bash'
      run: |-
        # Expand jbrLocationExpr
        jbr_location_expr='${{ runner.tool_cache }}/jbrsdk_jcef-21.0.6-osx-x64-b895.91.tar.gz'
        echo "jbrLocation=$jbr_location_expr" >> $GITHUB_OUTPUT
    - id: 'step-7'
      name: 'Get JBR 21 for macOS AArch64 (Attempt #1)'
      env:
        jbrLocation: '${{ steps.step-6.outputs.jbrLocation }}'
      continue-on-error: true
      timeout-minutes: 180
      shell: 'bash'
      run: |-
        jbr_location="$jbrLocation"
        checksum_url="https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.6-osx-x64-b895.91.tar.gz.checksum"
        checksum_file="checksum.tmp"
        wget -q -O $checksum_file $checksum_url

        expected_checksum=$(awk '{print $1}' $checksum_file)
        file_checksum=""

        if [ -f "$jbr_location" ]; then
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            wget -q --tries=3 https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.6-osx-x64-b895.91.tar.gz -O "$jbr_location"
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            echo "Checksum verification failed!" >&2
            rm -f $checksum_file
            exit 1
        fi

        rm -f $checksum_file
        file "$jbr_location"
    - id: 'step-8'
      name: 'Get JBR 21 for macOS AArch64 (Attempt #2)'
      env:
        jbrLocation: '${{ steps.step-6.outputs.jbrLocation }}'
      continue-on-error: false
      timeout-minutes: 180
      shell: 'bash'
      run: |-
        jbr_location="$jbrLocation"
        checksum_url="https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.6-osx-x64-b895.91.tar.gz.checksum"
        checksum_file="checksum.tmp"
        wget -q -O $checksum_file $checksum_url

        expected_checksum=$(awk '{print $1}' $checksum_file)
        file_checksum=""

        if [ -f "$jbr_location" ]; then
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            wget -q --tries=3 https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.6-osx-x64-b895.91.tar.gz -O "$jbr_location"
            file_checksum=$(shasum -a 512 "$jbr_location" | awk '{print $1}')
        fi

        if [ "$file_checksum" != "$expected_checksum" ]; then
            echo "Checksum verification failed!" >&2
            rm -f $checksum_file
            exit 1
        fi

        rm -f $checksum_file
        file "$jbr_location"
      if: '${{ steps.step-7.outcome == ''failure'' }}'
    - id: 'step-9'
      name: 'Setup JBR 21 for macOS '
      uses: 'gmitch215/setup-java@6d2c5e1f82f180ae79f799f0ed6e3e5efb4e664d'
      with:
        java-version: '21'
        distribution: 'jdkfile'
        jdkFile: '${{ steps.step-6.outputs.jbrLocation }}'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
    - id: 'step-10'
      name: 'Dump Local Properties'
      run: 'echo "jvm.toolchain.version=21" >> local.properties'
    - id: 'step-11'
      run: 'chmod -R 777 .'
    - id: 'step-12'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v3'
      with:
        cache-disabled: 'true'
    - id: 'step-13'
      name: 'Clean and download dependencies (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-14'
      name: 'Clean and download dependencies (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-13.outcome == ''failure'' }}'
    - id: 'step-15'
      name: 'Update Release Version Name (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-16'
      name: 'Update Release Version Name (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-15.outcome == ''failure'' }}'
    - id: 'step-17'
      name: 'Compile Kotlin (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-18'
      name: 'Compile Kotlin (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-17.outcome == ''failure'' }}'
    - id: 'step-19'
      name: 'Upload Desktop Installers (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
    - id: 'step-20'
      name: 'Upload Desktop Installers (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx6g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g" "-Pani.android.abis=arm64-v8a"'
      if: '${{ steps.step-19.outcome == ''failure'' }}'
    - id: 'step-21'
      name: 'Upload compose logs'
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'compose-logs-github-macos-13'
        path: 'app/desktop/build/compose/logs'
      if: '${{ always() }}'
  release_github-ubuntu-2404:
    name: 'Ubuntu 24.04 x86_64 (GitHub)'
    runs-on:
    - 'ubuntu-24.04'
    needs:
    - 'create-release'
    steps:
    - id: 'step-0'
      uses: 'actions/checkout@v4'
      with:
        submodules: 'recursive'
    - id: 'step-1'
      name: 'Get Tag'
      uses: 'dawidd6/action-get-tag@v1'
    - id: 'step-2'
      uses: 'bhowell2/github-substring-action@v1.0.0'
      with:
        value: '${{ steps.step-1.outputs.tag }}'
        index_of_str: 'v'
        default_return_value: '${{ steps.step-1.outputs.tag }}'
    - id: 'step-3'
      name: 'Free space for Ubuntu'
      uses: 'jlumbroso/free-disk-space@v1.3.1'
      with:
        android: 'false'
        large-packages: 'false'
        tool-cache: 'false'
    - id: 'step-4'
      name: 'Enable Swap'
      run: |-
        sudo fallocate -l 10G /swapfile
        sudo chmod 600 /swapfile
        sudo mkswap /swapfile
        sudo swapon /swapfile
    - id: 'step-5'
      continue-on-error: true
      run: 'rm local.properties'
    - id: 'step-6'
      continue-on-error: true
      run: |-
        echo "ani.dandanplay.app.id=${{ secrets.DANDANPLAY_APP_ID }}" >> local.properties
        echo "ani.dandanplay.app.secret=${{ secrets.DANDANPLAY_APP_SECRET }}" >> local.properties
        echo "ani.sentry.dsn=${{ secrets.SENTRY_DSN }}" >> local.properties
        echo "ani.analytics.server=${{ secrets.ANALYTICS_SERVER }}" >> local.properties
        echo "ani.analytics.key=${{ secrets.ANALYTICS_KEY }}" >> local.properties
        echo "kotlin.native.ignoreDisabledTargets=true" >> local.properties
    - id: 'step-7'
      name: 'Get JBR (Windows)'
      env:
        RUNNER_TOOL_CACHE: '${{ runner.tool_cache }}'
        JBR_URL: 'https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.5-linux-x64-b750.29.tar.gz'
        JBR_CHECKSUM_URL: 'https://cache-redirector.jetbrains.com/intellij-jbr/jbrsdk_jcef-21.0.5-linux-x64-b750.29.tar.gz.checksum'
      shell: 'bash'
      run: 'python .github/workflows/download_jbr.py'
    - id: 'step-8'
      name: 'Setup JBR 21 for Ubuntu'
      uses: 'gmitch215/setup-java@6d2c5e1f82f180ae79f799f0ed6e3e5efb4e664d'
      with:
        java-version: '21'
        distribution: 'jdkfile'
        jdkFile: '${{ steps.step-7.outputs.jbrLocation }}'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
    - id: 'step-9'
      name: 'Dump Local Properties'
      run: 'echo "jvm.toolchain.version=21" >> local.properties'
    - id: 'step-10'
      run: 'chmod -R 777 .'
    - id: 'step-11'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v3'
      with:
        cache-disabled: 'true'
    - id: 'step-12'
      name: 'Clean and download dependencies (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-13'
      name: 'Clean and download dependencies (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew --scan "--stacktrace" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-12.outcome == ''failure'' }}'
    - id: 'step-14'
      name: 'Update Release Version Name (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-15'
      name: 'Update Release Version Name (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew updateReleaseVersionNameFromGit "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-14.outcome == ''failure'' }}'
    - id: 'step-16'
      name: 'Prepare signing key'
      continue-on-error: true
      uses: 'timheuer/base64-to-file@v1.1'
      with:
        fileName: 'android_signing_key'
        fileDir: './'
        encodedString: '${{ secrets.SIGNING_RELEASE_STOREFILE }}'
      if: '${{ (github.repository == ''open-ani/animeko'') && (!(github.event_name == ''pull_request'')) }}'
    - id: 'step-17'
      name: 'Compile Kotlin (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-18'
      name: 'Compile Kotlin (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileKotlin compileCommonMainKotlinMetadata compileJvmMainKotlinMetadata compileKotlinDesktop compileKotlinMetadata "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-17.outcome == ''failure'' }}'
    - id: 'step-19'
      name: 'Compile Kotlin Android (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew compileDebugKotlinAndroid compileReleaseKotlinAndroid "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-20'
      name: 'Compile Kotlin Android (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew compileDebugKotlinAndroid compileReleaseKotlinAndroid "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-19.outcome == ''failure'' }}'
    - id: 'step-21'
      name: 'Build Android Debug APKs (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew assembleDebug "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-22'
      name: 'Build Android Debug APKs (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew assembleDebug "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-21.outcome == ''failure'' }}'
    - id: 'step-23'
      name: 'Upload Android Debug APK arm64-v8a (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-arm64-v8a-debug'
        path: 'app/android/build/outputs/apk/debug/android-arm64-v8a-debug.apk'
        overwrite: 'true'
    - id: 'step-24'
      name: 'Upload Android Debug APK arm64-v8a (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-arm64-v8a-debug'
        path: 'app/android/build/outputs/apk/debug/android-arm64-v8a-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-23.outcome == ''failure'' }}'
    - id: 'step-25'
      name: 'Upload Android Debug APK arm64-v8a (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-arm64-v8a-debug'
        path: 'app/android/build/outputs/apk/debug/android-arm64-v8a-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-24.outcome == ''failure'' }}'
    - id: 'step-26'
      name: 'Upload Android Debug APK x86_64 (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-x86_64-debug'
        path: 'app/android/build/outputs/apk/debug/android-x86_64-debug.apk'
        overwrite: 'true'
    - id: 'step-27'
      name: 'Upload Android Debug APK x86_64 (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-x86_64-debug'
        path: 'app/android/build/outputs/apk/debug/android-x86_64-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-26.outcome == ''failure'' }}'
    - id: 'step-28'
      name: 'Upload Android Debug APK x86_64 (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-x86_64-debug'
        path: 'app/android/build/outputs/apk/debug/android-x86_64-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-27.outcome == ''failure'' }}'
    - id: 'step-29'
      name: 'Upload Android Debug APK armeabi-v7a (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-armeabi-v7a-debug'
        path: 'app/android/build/outputs/apk/debug/android-armeabi-v7a-debug.apk'
        overwrite: 'true'
    - id: 'step-30'
      name: 'Upload Android Debug APK armeabi-v7a (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-armeabi-v7a-debug'
        path: 'app/android/build/outputs/apk/debug/android-armeabi-v7a-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-29.outcome == ''failure'' }}'
    - id: 'step-31'
      name: 'Upload Android Debug APK armeabi-v7a (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-armeabi-v7a-debug'
        path: 'app/android/build/outputs/apk/debug/android-armeabi-v7a-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-30.outcome == ''failure'' }}'
    - id: 'step-32'
      name: 'Upload Android Debug APK universal (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-universal-debug'
        path: 'app/android/build/outputs/apk/debug/android-universal-debug.apk'
        overwrite: 'true'
    - id: 'step-33'
      name: 'Upload Android Debug APK universal (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-universal-debug'
        path: 'app/android/build/outputs/apk/debug/android-universal-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-32.outcome == ''failure'' }}'
    - id: 'step-34'
      name: 'Upload Android Debug APK universal (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-universal-debug'
        path: 'app/android/build/outputs/apk/debug/android-universal-debug.apk'
        overwrite: 'true'
      if: '${{ steps.step-33.outcome == ''failure'' }}'
    - id: 'step-35'
      name: 'Build Android Release APKs (Attempt #1)'
      env:
        signing_release_storeFileFromRoot: '${{ steps.step-16.outputs.filePath }}'
        signing_release_storePassword: '${{ secrets.SIGNING_RELEASE_STOREPASSWORD }}'
        signing_release_keyAlias: '${{ secrets.SIGNING_RELEASE_KEYALIAS }}'
        signing_release_keyPassword: '${{ secrets.SIGNING_RELEASE_KEYPASSWORD }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew assembleRelease "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ (github.repository == ''open-ani/animeko'') && (!(github.event_name == ''pull_request'')) }}'
    - id: 'step-36'
      name: 'Build Android Release APKs (Attempt #2)'
      env:
        signing_release_storeFileFromRoot: '${{ steps.step-16.outputs.filePath }}'
        signing_release_storePassword: '${{ secrets.SIGNING_RELEASE_STOREPASSWORD }}'
        signing_release_keyAlias: '${{ secrets.SIGNING_RELEASE_KEYALIAS }}'
        signing_release_keyPassword: '${{ secrets.SIGNING_RELEASE_KEYPASSWORD }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew assembleRelease "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-35.outcome == ''failure'' }}'
    - id: 'step-37'
      name: 'Upload Android Release APK arm64-v8a (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-arm64-v8a-release'
        path: 'app/android/build/outputs/apk/release/android-arm64-v8a-release.apk'
        overwrite: 'true'
    - id: 'step-38'
      name: 'Upload Android Release APK arm64-v8a (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-arm64-v8a-release'
        path: 'app/android/build/outputs/apk/release/android-arm64-v8a-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-37.outcome == ''failure'' }}'
    - id: 'step-39'
      name: 'Upload Android Release APK arm64-v8a (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-arm64-v8a-release'
        path: 'app/android/build/outputs/apk/release/android-arm64-v8a-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-38.outcome == ''failure'' }}'
    - id: 'step-40'
      name: 'Upload Android Release APK x86_64 (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-x86_64-release'
        path: 'app/android/build/outputs/apk/release/android-x86_64-release.apk'
        overwrite: 'true'
    - id: 'step-41'
      name: 'Upload Android Release APK x86_64 (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-x86_64-release'
        path: 'app/android/build/outputs/apk/release/android-x86_64-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-40.outcome == ''failure'' }}'
    - id: 'step-42'
      name: 'Upload Android Release APK x86_64 (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-x86_64-release'
        path: 'app/android/build/outputs/apk/release/android-x86_64-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-41.outcome == ''failure'' }}'
    - id: 'step-43'
      name: 'Upload Android Release APK armeabi-v7a (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-armeabi-v7a-release'
        path: 'app/android/build/outputs/apk/release/android-armeabi-v7a-release.apk'
        overwrite: 'true'
    - id: 'step-44'
      name: 'Upload Android Release APK armeabi-v7a (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-armeabi-v7a-release'
        path: 'app/android/build/outputs/apk/release/android-armeabi-v7a-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-43.outcome == ''failure'' }}'
    - id: 'step-45'
      name: 'Upload Android Release APK armeabi-v7a (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-armeabi-v7a-release'
        path: 'app/android/build/outputs/apk/release/android-armeabi-v7a-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-44.outcome == ''failure'' }}'
    - id: 'step-46'
      name: 'Upload Android Release APK universal (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-universal-release'
        path: 'app/android/build/outputs/apk/release/android-universal-release.apk'
        overwrite: 'true'
    - id: 'step-47'
      name: 'Upload Android Release APK universal (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-universal-release'
        path: 'app/android/build/outputs/apk/release/android-universal-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-46.outcome == ''failure'' }}'
    - id: 'step-48'
      name: 'Upload Android Release APK universal (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-android-universal-release'
        path: 'app/android/build/outputs/apk/release/android-universal-release.apk'
        overwrite: 'true'
      if: '${{ steps.step-47.outcome == ''failure'' }}'
    - id: 'step-49'
      name: 'Upload Android APK for Release (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadAndroidApk "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-50'
      name: 'Upload Android APK for Release (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadAndroidApk "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-49.outcome == ''failure'' }}'
    - id: 'step-51'
      name: 'Generate QR code for APK (GitHub)'
      uses: 'snow-actions/qrcode@v1.0.0'
      with:
        text: 'https://github.com/open-ani/animeko/releases/download/${{ steps.step-1.outputs.tag }}/ani-${{ steps.step-2.outputs.substring }}-universal.apk'
        path: 'apk-qrcode-github.png'
    - id: 'step-52'
      name: 'Generate QR code for APK (Cloudflare)'
      uses: 'snow-actions/qrcode@v1.0.0'
      with:
        text: 'https://d.myani.org/${{ steps.step-1.outputs.tag }}/ani-${{ steps.step-2.outputs.substring }}-universal.apk'
        path: 'apk-qrcode-cloudflare.png'
    - id: 'step-53'
      name: 'Upload QR code (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadAndroidApkQR "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-54'
      name: 'Upload QR code (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadAndroidApkQR "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-53.outcome == ''failure'' }}'
    - id: 'step-55'
      name: 'Generate QR code for iOS (GitHub)'
      uses: 'snow-actions/qrcode@v1.0.0'
      with:
        text: 'https://github.com/open-ani/animeko/releases/download/${{ steps.step-1.outputs.tag }}/ani-${{ steps.step-2.outputs.substring }}.ipa'
        path: 'ipa-qrcode-github.png'
    - id: 'step-56'
      name: 'Generate QR code for iOS (Cloudflare)'
      uses: 'snow-actions/qrcode@v1.0.0'
      with:
        text: 'https://d.myani.org/${{ steps.step-1.outputs.tag }}/ani-${{ steps.step-2.outputs.substring }}.ipa'
        path: 'ipa-qrcode-cloudflare.png'
    - id: 'step-57'
      name: 'Upload QR code (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadIosIpaQR "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-58'
      name: 'Upload QR code (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadIosIpaQR "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-57.outcome == ''failure'' }}'
    - id: 'step-59'
      name: 'Package Desktop (Attempt #1)'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew createReleaseDistributable "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-60'
      name: 'Package Desktop (Attempt #2)'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew createReleaseDistributable "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-59.outcome == ''failure'' }}'
    - id: 'step-61'
      name: 'Upload compose logs'
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'compose-logs-github-ubuntu-2404'
        path: 'app/desktop/build/compose/logs'
      if: '${{ always() }}'
    - id: 'step-62'
      name: 'Build AppImage'
      run: |-
        # Download appimagetool
        wget https://github.com/AppImage/appimagetool/releases/download/continuous/appimagetool-x86_64.AppImage
        chmod +x appimagetool-x86_64.AppImage

        # Prepare AppDir
        mkdir -p AppDir/usr
        cp -r app/desktop/build/compose/binaries/main-release/app/Ani/* AppDir/usr

        cp app/desktop/appResources/linux-x64/AppRun AppDir/AppRun
        cp app/desktop/appResources/linux-x64/animeko.desktop AppDir/animeko.desktop
        cp app/desktop/appResources/linux-x64/icon.png AppDir/icon.png

        # Fix permissions
        chmod a+x AppDir/AppRun
        chmod a+x AppDir/usr/bin/Ani
        chmod a+x AppDir/usr/lib/runtime/lib/jcef_helper

        # Build AppImage
        ARCH=x86_64 ./appimagetool-x86_64.AppImage AppDir
    - id: 'step-63'
      name: 'Upload Linux packages (Attempt #1)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-linux-appimage-x64'
        path: 'Animeko-x86_64.AppImage'
        if-no-files-found: 'error'
        overwrite: 'true'
    - id: 'step-64'
      name: 'Upload Linux packages (Attempt #2)'
      continue-on-error: true
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-linux-appimage-x64'
        path: 'Animeko-x86_64.AppImage'
        if-no-files-found: 'error'
        overwrite: 'true'
      if: '${{ steps.step-63.outcome == ''failure'' }}'
    - id: 'step-65'
      name: 'Upload Linux packages (Attempt #3)'
      continue-on-error: false
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'ani-linux-appimage-x64'
        path: 'Animeko-x86_64.AppImage'
        if-no-files-found: 'error'
        overwrite: 'true'
      if: '${{ steps.step-64.outcome == ''failure'' }}'
    - id: 'step-66'
      name: 'Upload Desktop Installers (Attempt #1)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: true
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
    - id: 'step-67'
      name: 'Upload Desktop Installers (Attempt #2)'
      env:
        GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        GITHUB_REPOSITORY: '${{ secrets.GITHUB_REPOSITORY }}'
        CI_RELEASE_ID: '${{ needs.create-release.outputs.id }}'
        CI_TAG: '${{ steps.step-1.outputs.tag }}'
        UPLOAD_TO_S3: 'true'
        AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
        AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
        AWS_BASEURL: '${{ secrets.AWS_BASEURL }}'
        AWS_REGION: '${{ secrets.AWS_REGION }}'
        AWS_BUCKET: '${{ secrets.AWS_BUCKET }}'
      continue-on-error: false
      timeout-minutes: 180
      run: './gradlew :ci-helper:uploadDesktopInstallers "--no-configuration-cache" "--scan" "-Porg.gradle.daemon.idletimeout=60000" "-Dfile.encoding=UTF-8" "-Dorg.gradle.jvmargs=-Xmx8g -Dkotlin.daemon.jvm.options=-Xmx6g" "-Dkotlin.daemon.jvm.options=-Xmx6g"'
      if: '${{ steps.step-66.outcome == ''failure'' }}'
    - id: 'step-68'
      name: 'Upload compose logs'
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'compose-logs-github-ubuntu-2404'
        path: 'app/desktop/build/compose/logs'
      if: '${{ always() }}'
