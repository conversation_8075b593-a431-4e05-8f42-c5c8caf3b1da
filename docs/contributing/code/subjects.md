# 条目系统

Ani 的条目系统包括番剧、番剧的剧集和相关其他信息。

## 条目

每个条目（`Subject`）对应一个番剧的一个季度。

条目拥有以下重要属性：

- 唯一标识符 `subjectId`；
- 中文标题 `nameCn`。部分条目没有中文标题。例如 `BanG Dream! Ave Mujica`；
- 官方原标题 `name`。原标题可能是日文也可能是英文，取决于作品类型；
- 别名 `alias: List<String>`。顾名思义，别名就是该条目的其他语言的名称，或者是大家熟知的名称。
  例如，`别当哥哥了！` 拥有别名 `别当欧尼酱了！`。

> [!TIP]
> **条目数据来源**
>
> Ani 的条目数据目前完全使用 Bangumi，因此，Ani 的条目的所有数据和
> Bangumi 的都是一样的（包括 ID）。

## 条目系列和续集

番剧可能有多个季度（`Season`），每个季度都对应一个单独的条目。特别地：
- 如果有分割放送，则可能会有上半部分和下半部分**分别**是一个条目。
- 对于连续放送的半年番，通常不会分割上下季度，而是只有单个 25 话左右的条目。

例如，无职转生 动画系列的正片有以下条目：

- `无职转生～到了异世界就拿出真本事～`（第一季第一部分，共 11 话）
- `无职转生～到了异世界就拿出真本事～ 第2部分`（第一季第二部分，共 12 话）
- `无职转生Ⅱ ～到了异世界就拿出真本事～`（第二季第一部分，共 13 话）
- `无职转生Ⅱ ～到了异世界就拿出真本事～ 第2部分`（第二季第二部分，共 12 话）
- `无职转生Ⅲ ～到了异世界就拿出真本事～`（第三季，未定总话数）

我们可以称以上五个条目均属于同一 “无职转生” **系列**（`series`）。

在一个系列中，我们称任何后篇为前篇的**续集**条目（`sequel`）。例如，`无职转生Ⅱ ～到了异世界就拿出真本事～`（第二季第一部分）的续集条目包括：

- `无职转生Ⅱ ～到了异世界就拿出真本事～ 第2部分`
- `无职转生Ⅲ ～到了异世界就拿出真本事～`

> [!TIP]
>
> 系列和续集概念将在后续 MediaSelector 系统中发挥重要作用。

## 剧集

一个条目拥有多个剧集 `Episode`（又称章节）。例如第一集、第二集。

剧集拥有两种序号：

- 条目内序号 `ep`：该剧集在所属条目中的序号。例如，第二季条目中的第一集的 `ep = 1`。
- 系列序号 `sort`：该剧集在系列中的“总”序号。例如，第二季条目中的第一集的 `sort = 12 + 1 = 13`（假设前一季共
  12 集）。

条目内序号一般从 `01` 开始，但这不是必定的。序号不一定是整数，也可能是 `23.5` 的总集篇。

例如，对于“无职转生”系列，条目 `无职转生～到了异世界就拿出真本事～ 第2部分`（第一季第二部分）
中的第二集，有以下两个序号：

- `ep = 02`。“第二集” 在本条目中是第二个剧集，所以 `ep` 为 `02`；
- `sort = 13`。无职转生系列的第一季第一部分拥有 11 集，对于第一季第二部分中的“第二集”，它在系列中的序号是
  `11 + 2 = 13`。

> [!TIP]
> `01` 通常表示正片的序号。除了正片之外，条目可能会包含特数剧集，其序号通常表示为 `SP01`。稍后会解释。

### `EpisodeSort`

在代码中，我们统一使用 `EpisodeSort` 类型来封装“序号”。

`EpisodeSort` 类型本身不区分条目内序号 `ep` 和系列序号 `sort`，请不要从类型名称里的 `Sort`
认为它一定表示系列序号。具体表示哪种序号需要参考上下文。

> [!TIP]
> 本文档总是会使用全称“系列序号”和“条目内序号”区分两种序号。

## 条目和剧集类型

上文只考虑了正片，现在我们考虑 OVA 和剧场版等类型。

在 Bangumi 上，OVA 和 SP 有两种形式存在。有可能是一个独立的 OVA/SP 条目（包含单个剧集），也有可能是作为一个特殊剧集归属于主条目中。

Ani
的条目系统暂未考虑上述类型，但这在近期计划中 [#492](https://github.com/open-ani/animeko/issues/492)。
