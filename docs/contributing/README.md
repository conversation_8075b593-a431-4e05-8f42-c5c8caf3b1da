# 参与开发

欢迎你提交 PR 参与开发。

## 获取帮助

开发文档一直都是一个进行中的工作。如你对项目结构有任何疑问，欢迎通过以下途径寻求帮助：

- Telegram
  开发者群 [![Group](https://img.shields.io/badge/Telegram-2CA5E0?style=flat-squeare&logo=telegram&logoColor=white)](https://t.me/openani_dev) (
  推荐，开发者即时回复)
- [GitHub Discussions](https://github.com/open-ani/ani/discussions)

## 上手指南

> [!IMPORTANT]
> 每个步骤都很重要，根据你的经验不同，总共可能需要花费 10-30 分钟。请不要跳过，跳过可能会导致花费更多时间解决问题。

1. [开发工具: IDE, JDK, 推荐插件](setup.md)
2. [代码风格与代码规范](code-style.md)
3. [项目架构](architecture.md)
4. [构建和打包](building.md): 如何编译, 如何打包 APK
5. [编写测试和调试 APP](testing.md)
6. [一些开发提示](dev-tips.md): 预览 Compose UI

## 开发文档

> 以下文档为人工编写，只覆盖部分内容。
> 可以参考 DeepWiki (AI) 自动生成的全面在线文档（有很高正确性）：<https://deepwiki.com/open-ani/animeko>

- [条目系统](code/subjects.md)
- [Media Framework](code/media-framework.md)
    - [MediaSource](code/media/media-source.md)
    - [MediaSelector](code/media/media-selector.md)
    - [缓存](code/media/media-cache.md)

## 更多信息

- [查找待解决的问题](issues.md) (如何查阅 issues)
- [PR 审核](pr-review.md)
