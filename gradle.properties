#
# Copyright (C) 2024-2025 OpenAni and contributors.
#
# \u6B64\u6E90\u4EE3\u7801\u7684\u4F7F\u7528\u53D7 GNU AFFERO GENERAL PUBLIC LICENSE version 3 \u8BB8\u53EF\u8BC1\u7684\u7EA6\u675F, \u53EF\u4EE5\u5728\u4EE5\u4E0B\u94FE\u63A5\u627E\u5230\u8BE5\u8BB8\u53EF\u8BC1.
# Use of this source code is governed by the GNU AGPLv3 license, which can be found at the following link.
#
# https://github.com/open-ani/ani/blob/main/LICENSE
#
kotlin.code.style=official
android.useAndroidX=true
org.gradle.jvmargs=-Xmx6g -Dfile.encoding=UTF-8 -Dkotlin.daemon.jvm.options\="-Xmx4096M"
# Project version, automatically updated by task :ci-helper:updateDevVersionNameFromGit
version.name=4.9.0-dev
# package.version must be major.minor.patch without meta
package.version=5.0.0
kotlin.mpp.androidSourceSetLayoutVersion=2
org.gradle.caching=true
org.gradle.configuration-cache=true
# `android.version.code` Also used in ios
# major(1)-minor(pad to 2)-patch(1)-meta(1)
android.version.code=30200
android.compile.sdk=35
android.min.sdk=27
# JBR must have JCEF bundled
jvm.toolchain.vendor=jetbrains
jvm.toolchain.version=21
kotlin.mpp.enableCInteropCommonization=true
kotlin.apple.xcodeCompatibility.nowarn=true
kotlin.native.ignoreDisabledTargets=true
